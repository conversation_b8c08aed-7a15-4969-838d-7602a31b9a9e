## TaskMint Rebranding Implementation Checklist

**Important Instructions for AI/Developer:**
*   **Case Sensitivity:** When replacing "Time Tracker", be mindful of case variations (e.g., "time tracker", "TimeTracker"). The AI should attempt to match the original casing for "TaskMint" (e.g., "taskmint", "TaskMint").
*   **Theme Application:** References to `THEME.md` for colors and fonts imply updating the MUI theme configuration files primarily (`src/theme.ts`, `src/theme/lightTheme.ts`). Component-specific styles should only be added if the theme doesn't cover it.
*   **Asset Replacement:** Tasks marked "Update Asset (Manual)" require human intervention to replace image files. The AI should note these locations.
*   **Contextual Review:** Some tasks, especially in documentation, require contextual understanding beyond simple string replacement.

---

**Phase 1: Core Naming, Identifiers, and Configuration**

1.  **File:** `package.json` ✅ **COMPLETED**
    *   **Action Type:** Update JSON Value
    *   **Target Key:** `name`
    *   **Old Value:** `"time-tracker"`
    *   **New Value:** `"taskmint"`
    *   **Notes:** Ensure it's lowercase, valid for npm.

2.  **File:** `src-tauri/tauri.conf.json` ✅ **COMPLETED**
    *   **Action Type:** Update JSON Value
    *   **Target Key:** `productName`
    *   **Old Value:** `"Time Tracker"`
    *   **New Value:** `"TaskMint"`

3.  **File:** `src-tauri/tauri.conf.json` ✅ **COMPLETED**
    *   **Action Type:** Update JSON Value
    *   **Target Key:** `identifier`
    *   **Old Value:** `"com.timetracker.app"`
    *   **New Value:** `"com.taskmint.app"`

4.  **File:** `index.html` ✅ **COMPLETED**
    *   **Action Type:** Update HTML Content
    *   **Target Element:** `<title>` tag
    *   **Old Value:** (Likely `"Tauri + React + Typescript"` or `"Time Tracker"`)
    *   **New Value:** `"TaskMint"`

5.  **File:** `src-tauri/Cargo.toml` ✅ **COMPLETED**
    *   **Action Type:** Update TOML Value
    *   **Target Key:** `package.name`
    *   **Old Value:** `"time-tracker"`
    *   **New Value:** `"taskmint"`
    *   **Notes:** Also check `package.description` if it contains "Time Tracker".

6.  **File:** `src-tauri/Cargo.toml` ✅ **COMPLETED**
    *   **Action Type:** Update TOML Value
    *   **Target Key:** `lib.name`
    *   **Old Value:** `"time_tracker_lib"`
    *   **New Value:** `"taskmint_lib"`
    *   **Notes:** This change will require updating the `main.rs` import (`time_tracker_lib::run()` -> `taskmint_lib::run()`).

7.  **File:** `src-tauri/src/main.rs` ✅ **COMPLETED**
    *   **Action Type:** Update Rust Code
    *   **Target Element:** `use` statement and function call.
    *   **Old Value:** `time_tracker_lib::run()`
    *   **New Value:** `taskmint_lib::run()` (and `use taskmint_lib;` if `time_tracker_lib` was used directly).

---

**Phase 2: UI Text and Content (Code)**

1.  **File:** `src/components/layout/Sidebar.tsx` ✅ **COMPLETED**
    *   **Action Type:** Replace String in JSX
    *   **Target Element:** `Typography` component displaying the app title.
    *   **Text to Find:** `"Time Tracker"`
    *   **Replacement Text:** `"TaskMint"`

2.  **File:** `src/__mocks__/tauri-api.ts` ✅ **COMPLETED**
    *   **Action Type:** Update Mock Implementation
    *   **Target Element:** `getName` mock function's resolved value.
    *   **Code Snippet to Find:** `getName = jest.fn().mockResolvedValue('Time Tracker');`
    *   **Replacement Code Snippet:** `getName = jest.fn().mockResolvedValue('TaskMint');`

3.  **File:** `src/__mocks__/@tauri-apps/api/core.ts` ✅ **COMPLETED** (No getName mock found)
    *   **Action Type:** Update Mock Implementation (if `getName` is mocked here too)
    *   **Target Element:** Case for `getName` if it exists.
    *   **Notes:** Review if app name is mocked here and update accordingly.

4.  **File:** `src-tauri/src/lib.rs` ✅ **COMPLETED**
    *   **Action Type:** Update String Literal (Rust)
    *   **Target Element:** `tooltip` in `TrayIconBuilder` (multiple places if updated dynamically).
    *   **String to Find:** `"Time Tracker"` or `"Time Tracker - No active timer"`
    *   **Replacement Pattern:** `"TaskMint"` or `"TaskMint - No active timer"`

5.  **File:** `src/hooks/useDataBackup.ts`
    *   **Action Type:** Update String Literal
    *   **Target Element:** `exportedBy` field within `createBackupData` function.
    *   **String to Find:** `"Time Tracker App"`
    *   **Replacement Text:** `"TaskMint"` (or "TaskMint App")

6.  **File:** `src/App.tsx`
    *   **Action Type:** Review/Update Window Title (if set programmatically in React)
    *   **Target Element:** Any `document.title` assignments or props passed to a window title component.
    *   **Notes:** Primary window title is usually set in `tauri.conf.json`. This is a check for overrides.

**Phase 3: Theme and Styling Application (MUI)**

**Global Theme Updates:**

1.  **File:** `src/theme.ts` (Dark Theme)
    *   **Action Type:** Update MUI Theme Configuration
    *   **Target Key:** `palette.primary.main`
    *   **New Value:** `"#65D6A1"` (Mint Green)
    *   **Branding Reference:** `THEME.md` - Primary Colors.
    *   **Notes:** Adjust `light` and `dark` shades of `primary` to complement.

2.  **File:** `src/theme.ts` (Dark Theme)
    *   **Action Type:** Update MUI Theme Configuration
    *   **Target Key:** `palette.secondary.main`
    *   **New Value:** `"#0D7377"` (Deep Teal)
    *   **Branding Reference:** `THEME.md` - Primary Colors.
    *   **Notes:** Adjust `light` and `dark` shades of `secondary`.

3.  **File:** `src/theme/lightTheme.ts` (Light Theme)
    *   **Action Type:** Update MUI Theme Configuration
    *   **Target Key:** `palette.primary.main`
    *   **New Value:** `"#65D6A1"` (Mint Green)
    *   **Branding Reference:** `THEME.md` - Primary Colors.
    *   **Notes:** Adjust `light` and `dark` shades.

4.  **File:** `src/theme/lightTheme.ts` (Light Theme)
    *   **Action Type:** Update MUI Theme Configuration
    *   **Target Key:** `palette.secondary.main`
    *   **New Value:** `"#0D7377"` (Deep Teal)
    *   **Branding Reference:** `THEME.md` - Primary Colors.
    *   **Notes:** Adjust `light` and `dark` shades.

5.  **Files:** `src/theme.ts`, `src/theme/lightTheme.ts`
    *   **Action Type:** Update MUI Theme Configuration
    *   **Target Key:** `palette.background.default`, `palette.background.paper`
    *   **New Value (Light):** `default: "#FAFAFA"`, `paper: "#ffffff"`
    *   **New Value (Dark):** (Define appropriate dark shades for Mint/Teal. `THEME.md` uses `#121212` and `#1e1e1e` for dark theme; ensure these work well with new primary/secondary).
    *   **Branding Reference:** `THEME.md` - Accent Colors.

6.  **Files:** `src/theme.ts`, `src/theme/lightTheme.ts`
    *   **Action Type:** Update MUI Theme Configuration
    *   **Target Key:** `palette.text.primary`, `palette.text.secondary`
    *   **New Value (Light):** `primary: "#3F4E4F"` (Slate Gray), `secondary: (a lighter Slate Gray or medium gray)`
    *   **New Value (Dark):** `primary: "#FAFAFA"`, `secondary: (e.g., #b0b0b0)`
    *   **Branding Reference:** `THEME.md` - Accent Colors.

7.  **Files:** `src/theme.ts`, `src/theme/lightTheme.ts`
    *   **Action Type:** Update MUI Theme Configuration
    *   **Target Keys:** `palette.success.main`, `palette.warning.main`, `palette.error.main`
    *   **New Values:** `success: "#32CD32"`, `warning: "#FFD700"`, `error: "#FF5C5C"`
    *   **Branding Reference:** `THEME.md` - Notification & Feedback Colors.
    *   **Notes:** Ensure `contrastText` provides good accessibility.

8.  **Files:** `src/theme.ts`, `src/theme/lightTheme.ts`
    *   **Action Type:** Update MUI Theme Configuration
    *   **Target Key:** `typography.fontFamily`
    *   **New Value:** `"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif` (or `"Poppins"`)
    *   **Branding Reference:** `THEME.md` - Typography.

9.  **Files:** `src/theme.ts`, `src/theme/lightTheme.ts`
    *   **Action Type:** Update MUI Theme Configuration
    *   **Target Key:** `shape.borderRadius`
    *   **New Value:** `12` (if globally changing from current `8`)
    *   **Branding Reference:** `THEME.md` - Visual Style (Rounded corners).
    *   **Notes:** If `12px` is for specific components like buttons, update in `components` overrides instead.

**Component-Specific Styling:**

10. **File:** `src/components/layout/GlobalTimerBar.tsx`
    *   **Action Type:** Apply Styling
    *   **Target Element:** `Paper` component (main timer bar background).
    *   **Styling:** Background: Deep Teal (`#0D7377`).
    *   **Branding Reference:** `THEME.md` - Sample UI Elements.

11. **File:** `src/components/ui/display/TimerDisplay.tsx` or `GlobalTimerBar.tsx`
    *   **Action Type:** Apply Styling
    *   **Target Element:** Active timer indicator/progress element.
    *   **Styling:** Color: Mint Green (`#65D6A1`).
    *   **Branding Reference:** `THEME.md` - Sample UI Elements.

12. **File:** Components displaying timers, earnings, numbers (e.g., `TimerDisplay.tsx`, `EarningsDisplay.tsx`)
    *   **Action Type:** Apply Font
    *   **Target Element:** Text elements for numerical data.
    *   **Styling:** `fontFamily: "Roboto Mono", monospace`.
    *   **Branding Reference:** `THEME.md` - Typography.

13. **File:** `src/components/ui/display/DailyGoalProgressWidget.tsx`
    *   **Action Type:** Apply Styling
    *   **Target Element:** Circular progress indicator.
    *   **Styling:** Color: Mint Green (`#65D6A1`).
    *   **Branding Reference:** `THEME.md` - Sample UI Elements.

14. **File:** `src/components/features/notes/NoteEditor.tsx` and related notes components.
    *   **Action Type:** Apply Styling
    *   **Target Element:** Command execution fields.
    *   **Styling:** `fontFamily: "Roboto Mono", monospace`, `backgroundColor: "#E8F8F5"` (light mint/teal).
    *   **Branding Reference:** `THEME.md` - Sample UI Elements.
    *   **Notes:** Outline other note interface elements lightly in Deep Teal (`#0D7377`).

**Phase 4: Assets and Icons**

1.  **Directory:** `src-tauri/icons/`
    *   **Action Type:** Update Asset (Manual)
    *   **Target Files:** `32x32.png`, `128x128.png`, `<EMAIL>`, `icon.icns`, `icon.ico`
    *   **Task:** Replace these with the new TaskMint app icons. The logo concept is a "stylized mint leaf combined subtly with a checkmark or clock icon."
    *   **Branding Reference:** `THEME.md` - Logo Concept.

2.  **File:** `public/vite.svg`
    *   **Action Type:** Update Asset (Manual) or Remove/Replace Link
    *   **Target Element:** Browser tab favicon.
    *   **Task:** Replace `vite.svg` with a TaskMint favicon (e.g., simplified logo symbol). Update `index.html` `<link rel="icon" ...>` if the filename changes.
    *   **Branding Reference:** `THEME.md` - Logo Concept.

3.  **File:** `public/tauri.svg`
    *   **Action Type:** Update Asset (Manual)
    *   **Target Element:** SVG image.
    *   **Task:** Replace with a TaskMint logo SVG.
    *   **Branding Reference:** `THEME.md` - Logo Concept.

4.  **Component Review:** `src/App.tsx` and any other components directly using `tauri.svg` or `vite.svg`.
    *   **Action Type:** Update Asset Import/Usage
    *   **Task:** If these SVGs are imported and used as React components, update the import paths if filenames changed, or update the components to use new TaskMint SVGs.

5.  **MUI Icons Review:**
    *   **Action Type:** Review and Potentially Replace
    *   **Target Element:** MUI icons used throughout the app.
    *   **Task:** Ensure icons align with the "flat, simple line icons" style. Consider alternatives if current icons clash with the new branding. This is more subjective.
    *   **Branding Reference:** `THEME.md` - Iconography.

**Phase 5: Documentation and Metadata**

1.  **File:** `README.md`
    *   **Action Type:** Full Content Update
    *   **Task:**
        *   Replace all instances of "Time Tracker" with "TaskMint".
        *   Update the app description to reflect TaskMint's branding and features.
        *   Incorporate new taglines/slogans from `THEME.md`.
        *   Update any links or references if the project's URL/repository name changes.
        *   Add `Website: https://taskmint.app` (if applicable).
    *   **Branding Reference:** Full `THEME.md`.

2.  **Files:** `BUG-FIXES.md`, `GOALS.md`
    *   **Action Type:** Replace String
    *   **Text to Find:** "Time Tracker"
    *   **Replacement Text:** "TaskMint"

3.  **Source Code Comments:**
    *   **Action Type:** Find and Replace String (Global, Contextual)
    *   **Text to Find:** "Time Tracker"
    *   **Replacement Text:** "TaskMint"
    *   **Notes:** Search all `.ts`, `.tsx`, `.rs`, `.md`, `.json`, `.toml` files for "Time Tracker" and replace where appropriate.

4.  **File:** `.env.example` (and `.env` if it exists and is committed, though it shouldn't be)
    *   **Action Type:** Review Content
    *   **Task:** Ensure no internal naming references "Time Tracker" if they should be "TaskMint" (e.g., if API keys were named like `TIME_TRACKER_API_KEY`).

**Phase 6: Final Review and Testing**

1.  **All Files:**
    *   **Action Type:** Comprehensive Review
    *   **Task:**
        *   Globally search for any remaining instances of "Time Tracker" or "timetracker".
        *   Build and run the application on all target platforms (macOS, Windows).
        *   Thoroughly test all UI elements for correct new branding application (colors, fonts, text).
        *   Verify all functionality remains intact.
        *   Check system tray icon, tooltip, and menu items.
        *   Test notifications for correct branding.
        *   Verify data export filenames and internal metadata (`exportedBy`).
        *   Check window titles.
        *   Ensure good color contrast and accessibility with the new theme.
    *   **Branding Reference:** Full `THEME.md`.

